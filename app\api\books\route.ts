import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { books, booksLanguages } from '@/drizzle/schema';
import { put } from '@vercel/blob';
import { eq, and } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  console.log('--- POST Request Received ---');
  try {
    const formData = await request.formData();
    console.log('FormData received:', Object.fromEntries(formData.entries()));

    const namaBuku = formData.get('namaBuku') as string;
    const description = formData.get('description') as string;
    const pages = parseInt(formData.get('pages') as string);
    const license = formData.get('license') as string;
    const author = formData.get('author') as string;
    const harga = parseFloat(formData.get('harga') as string);
    const rating = parseFloat(formData.get('rating') as string);
    const coverImage = formData.get('coverImage') as File;
    const originalPdf = formData.get('originalPdf') as File;
    const languages = JSON.parse(formData.get('languages') as string);
    const category = formData.get('category') as string;

    console.log('Parsed form data:', {
      namaBuku,
      description,
      pages,
      license,
      author,
      harga,
      rating,
      coverImageName: coverImage ? coverImage.name : 'No cover image',
      originalPdfName: originalPdf ? originalPdf.name : 'No original PDF',
      languages,
      category,
    });
    console.log('Attempting to upload cover image...');
    const { url: coverImageUrl } = await put(
      `covers/${Date.now()}-${coverImage.name}`,
      coverImage,
      { access: 'public' }
    );
    console.log('Cover image uploaded, URL:', coverImageUrl);

    console.log('Attempting to upload original PDF...');
    const { url: originalPdfUrl } = await put(
      `pdfs/original/${Date.now()}-${originalPdf.name}`,
      originalPdf,
      { access: 'public' }
    );
    console.log('Original PDF uploaded, URL:', originalPdfUrl);

    // Insert book
    console.log('Attempting to insert new book into database...');
    const [newBook] = await db.insert(books).values({
      namaBuku,
      description,
      pages,
      license,
      author,
      harga,
      rating,
      coverImageUrl,
      originalPdfUrl,
      category,
    }).returning();
    console.log('New book inserted:', newBook);

    console.log('Attempting to insert languages and translated PDFs...');
    for (let i = 0; i < languages.length; i++) {
    const lang = languages[i];
    const pdfFile = formData.get(`languagePdf-${i}`) as File | null;

    if (!pdfFile) {
        // Tidak ada PDF baru → skip upload
        console.log(`No new PDF for language ${lang.language}, skipping upload.`);
        continue;
    }

    console.log(`Uploading translated PDF for language: ${lang.language}...`);
    const { url: translatedPdfUrl } = await put(
        `pdfs/translated/${lang.language}-${Date.now()}-${pdfFile.name}`,
        pdfFile,
        { access: 'public' }
    );
    console.log(`Translated PDF for ${lang.language} uploaded, URL: ${translatedPdfUrl}`);

    await db.insert(booksLanguages).values({
        idBuku: newBook.id,
        language: lang.language,
        translatedPdfUrl,
    });
    console.log(`Language entry for ${lang.language} inserted for book ID: ${newBook.id}`);
    }

    console.log('Book and associated languages created successfully.');
    return NextResponse.json(newBook);
  } catch (error) {
    console.error('Error in POST /api/books:', error);
    return NextResponse.json({ error: 'Failed to create book' }, { status: 500 });
  } finally {
    console.log('--- POST Request Finished ---');
  }
}

export async function GET() {
  try {
    // Ambil semua buku
    const allBooks = await db.select().from(books);

    // Ambil semua bahasa untuk semua buku (1 query lebih hemat)
    const allLangs = await db.select().from(booksLanguages);

    // Gabungkan ke masing-masing buku
    const booksWithLangs = allBooks.map(book => ({
      ...book,
      languages: allLangs.filter(l => l.idBuku === book.id)
    }));

    return NextResponse.json(booksWithLangs);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch books' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  console.log('--- PUT Request Received ---');
  try {
    const formData = await request.formData();
    
    const namaBuku = formData.get('namaBuku') as string;
    const language = formData.get('language') as string;
    const translatedPdf = formData.get('translatedPdf') as File;

    console.log('PUT request data:', { namaBuku, language, translatedPdfName: translatedPdf?.name });

    // Cari buku berdasarkan nama
    const [book] = await db.select().from(books).where(eq(books.namaBuku, namaBuku));
    
    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    // Upload translated PDF
    const { url: translatedPdfUrl } = await put(
      `pdfs/translated/${language}-${Date.now()}-${translatedPdf.name}`,
      translatedPdf,
      { access: 'public' }
    );

    // Check if language entry already exists
    const [existingLang] = await db.select()
      .from(booksLanguages)
      .where(and(
        eq(booksLanguages.idBuku, book.id),
        eq(booksLanguages.language, language)
      ));

    if (existingLang) {
      // Update existing entry
      await db.update(booksLanguages)
        .set({ translatedPdfUrl })
        .where(and(
          eq(booksLanguages.idBuku, book.id),
          eq(booksLanguages.language, language)
        ));
    } else {
      // Insert new entry
      await db.insert(booksLanguages).values({
        idBuku: book.id,
        language,
        translatedPdfUrl,
      });
    }

    console.log('Translation updated successfully');
    return NextResponse.json({ success: true, translatedPdfUrl });
  } catch (error) {
    console.error('Error in PUT /api/books:', error);
    return NextResponse.json({ error: 'Failed to update translation' }, { status: 500 });
  }
}