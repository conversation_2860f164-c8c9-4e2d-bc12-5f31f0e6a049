import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { books, booksLanguages } from '@/drizzle/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  console.log('--- POST Request Received ---');
  try {
    const formData = await request.formData();
    console.log('FormData received:', Object.fromEntries(formData.entries()));

    const namaBuku = formData.get('namaBuku') as string;
    const description = formData.get('description') as string;
    const pages = parseInt(formData.get('pages') as string);
    const license = formData.get('license') as string;
    const author = formData.get('author') as string;
    const harga = parseFloat(formData.get('harga') as string);
    const rating = parseFloat(formData.get('rating') as string);
    const coverImageUrl = formData.get('coverImageUrl') as string;
    const originalPdfUrl = formData.get('originalPdfUrl') as string;
    const languages = JSON.parse(formData.get('languages') as string);
    const category = formData.get('category') as string;

    console.log('Parsed form data:', {
      namaBuku,
      description,
      pages,
      license,
      author,
      harga,
      rating,
      coverImageUrl,
      originalPdfUrl,
      languages,
      category,
    });

    if (!coverImageUrl || !originalPdfUrl) {
      return NextResponse.json({ error: 'Cover image and original PDF URLs are required' }, { status: 400 });
    }

    // Insert book
    console.log('Attempting to insert new book into database...');
    const [newBook] = await db.insert(books).values({
      namaBuku,
      description,
      pages,
      license,
      author,
      harga,
      rating,
      coverImageUrl,
      originalPdfUrl,
      category,
    }).returning();
    console.log('New book inserted:', newBook);

    console.log('Attempting to insert languages and translated PDFs...');
    for (let i = 0; i < languages.length; i++) {
      const lang = languages[i];
      const translatedPdfUrl = formData.get(`languagePdfUrl-${i}`) as string | null;

      if (!translatedPdfUrl) {
        // Tidak ada PDF URL → skip
        console.log(`No PDF URL for language ${lang.language}, skipping.`);
        continue;
      }

      console.log(`Adding translated PDF URL for language: ${lang.language}...`);
      await db.insert(booksLanguages).values({
        idBuku: newBook.id,
        language: lang.language,
        translatedPdfUrl,
      });
      console.log(`Language entry for ${lang.language} inserted for book ID: ${newBook.id}`);
    }

    console.log('Book and associated languages created successfully.');
    return NextResponse.json(newBook);
  } catch (error) {
    console.error('Error in POST /api/books:', error);
    return NextResponse.json({ error: 'Failed to create book' }, { status: 500 });
  } finally {
    console.log('--- POST Request Finished ---');
  }
}

export async function GET() {
  try {
    // Ambil semua buku
    const allBooks = await db.select().from(books);

    // Ambil semua bahasa untuk semua buku (1 query lebih hemat)
    const allLangs = await db.select().from(booksLanguages);

    // Gabungkan ke masing-masing buku
    const booksWithLangs = allBooks.map(book => ({
      ...book,
      languages: allLangs.filter(l => l.idBuku === book.id)
    }));

    return NextResponse.json(booksWithLangs);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch books' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  console.log('--- PUT Request Received ---');
  try {
    const formData = await request.formData();

    const namaBuku = formData.get('namaBuku') as string;
    const language = formData.get('language') as string;
    const translatedPdfUrl = formData.get('translatedPdfUrl') as string;

    console.log('PUT request data:', { namaBuku, language, translatedPdfUrl });

    if (!translatedPdfUrl) {
      return NextResponse.json({ error: 'Translated PDF URL is required' }, { status: 400 });
    }

    // Cari buku berdasarkan nama
    const [book] = await db.select().from(books).where(eq(books.namaBuku, namaBuku));

    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    // Check if language entry already exists
    const [existingLang] = await db.select()
      .from(booksLanguages)
      .where(and(
        eq(booksLanguages.idBuku, book.id),
        eq(booksLanguages.language, language)
      ));

    if (existingLang) {
      // Update existing entry
      await db.update(booksLanguages)
        .set({ translatedPdfUrl })
        .where(and(
          eq(booksLanguages.idBuku, book.id),
          eq(booksLanguages.language, language)
        ));
    } else {
      // Insert new entry
      await db.insert(booksLanguages).values({
        idBuku: book.id,
        language,
        translatedPdfUrl,
      });
    }

    console.log('Translation updated successfully');
    return NextResponse.json({ success: true, translatedPdfUrl });
  } catch (error) {
    console.error('Error in PUT /api/books:', error);
    return NextResponse.json({ error: 'Failed to update translation' }, { status: 500 });
  }
}