// utils/fontMapper.ts
export const FONT_MAP: Record<string, string> = {
  'BookAntiqua':            '/fonts/bookantiqua.ttf',
  'BookAntiqua-Bold':       '/fonts/bookantiqua_bold.ttf',
  'MuseoSans-100':          '/fonts/MuseoSans-100.otf',
  'MuseoSans-100Italic':    '/fonts/MuseoSans-100Italic.otf',
  'MuseoSans-300':          '/fonts/MuseoSans-300.otf',
  'MuseoSans-300Italic':    '/fonts/MuseoSans-300Italic.otf',
  'MuseoSans-500':          '/fonts/MuseoSans-500.otf',
  'MuseoSans-500Italic':    '/fonts/MuseoSans-500Italic.otf',
  'MuseoSans-700':          '/fonts/MuseoSans-700.otf',
  'MuseoSans-700Italic':    '/fonts/MuseoSans-700Italic.otf',
  'MuseoSans-900':          '/fonts/MuseoSans-900.otf',
  'MuseoSans-900Italic':    '/fonts/MuseoSans-900Italic.otf',
  // Fallback fonts untuk Unicode support
  'NotoSans-Regular':       '/fonts/NotoSans-Regular.ttf',
  'NotoSans-Bold':          '/fonts/NotoSans-Bold.ttf',
  'NotoSans-Italic':        '/fonts/NotoSans-Italic.ttf',
  'NotoSans-BoldItalic':    '/fonts/NotoSans-BoldItalic.ttf',
  // Arabic fonts (hanya Regular dan Bold)
  'NotoSansArabic-Regular': '/fonts/NotoSansArabic-Regular.ttf',
  'NotoSansArabic-Bold':    '/fonts/NotoSansArabic-Bold.ttf',
  // Japanese fonts (hanya Regular dan Bold)
  'NotoSansJP-Regular':     '/fonts/NotoSansJP-Regular.ttf',
  'NotoSansJP-Bold':        '/fonts/NotoSansJP-Bold.ttf',
};

// Font fallback mapping berdasarkan script/bahasa
export const FALLBACK_FONTS: Record<string, string> = {
  // Cyrillic (Russian, Bulgarian, etc.)
  'cyrillic': 'NotoSans-Regular',
  // CJK (Chinese, Japanese, Korean)
  'cjk': 'NotoSansJP-Regular',
  // Arabic
  'arabic': 'NotoSansArabic-Regular',
  // Default fallback
  'default': 'NotoSans-Regular'
};

// Fungsi untuk mendeteksi script/bahasa dari teks
export const detectTextScript = (text: string): string => {
  // Regex patterns untuk berbagai script
  const patterns = {
    arabic: /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/,
    // Japanese (Hiragana, Katakana, Kanji)
    japanese: /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]/,
    // Chinese (Simplified & Traditional)
    chinese: /[\u4E00-\u9FFF\u3400-\u4DBF]/,
    // Korean
    korean: /[\uAC00-\uD7AF]/,
    cyrillic: /[\u0400-\u04FF\u0500-\u052F\u2DE0-\u2DFF\uA640-\uA69F]/,
  };

  // Prioritas deteksi: Arabic > Japanese > Chinese > Korean > Cyrillic
  for (const [script, pattern] of Object.entries(patterns)) {
    if (pattern.test(text)) {
      // Untuk CJK, kita gunakan Japanese font sebagai fallback utama
      if (script === 'chinese' || script === 'korean') {
        return 'cjk';
      }
      return script;
    }
  }

  return 'latin';
};

// Fungsi untuk mengecek apakah font mendukung karakter tertentu
export const checkFontSupport = async (fontPath: string, text: string): Promise<boolean> => {
  try {
    const script = detectTextScript(text);

    // MuseoSans dan BookAntiqua hanya mendukung Latin
    if (fontPath.includes('MuseoSans') || fontPath.includes('BookAntiqua')) {
      return script === 'latin';
    }

    // NotoSans umum mendukung Latin dan Cyrillic
    if (fontPath.includes('NotoSans-') && !fontPath.includes('Arabic') && !fontPath.includes('JP')) {
      return script === 'latin' || script === 'cyrillic';
    }

    // NotoSansArabic hanya mendukung Arabic dan Latin
    if (fontPath.includes('NotoSansArabic')) {
      return script === 'arabic' || script === 'latin';
    }

    // NotoSansJP mendukung Japanese, Chinese, Korean, dan Latin
    if (fontPath.includes('NotoSansJP')) {
      return script === 'japanese' || script === 'chinese' || script === 'korean' || script === 'cjk' || script === 'latin';
    }

    // Font sistem (Helvetica, Arial, dll) hanya mendukung Latin
    return script === 'latin';
  } catch (error) {
    console.warn('Error checking font support:', error);
    return false;
  }
};

// Fungsi untuk mendapatkan font fallback yang tepat
export const getFallbackFont = (originalFont: string, text: string, weight: string, italic: boolean): string => {
  const script = detectTextScript(text);

  // Jika teks hanya Latin, gunakan font asli
  if (script === 'latin') {
    return originalFont;
  }

  // Mapping weight untuk fallback fonts
  const weightMap: Record<string, string> = {
    'normal': 'Regular',
    'bold': 'Bold',
    'Bold': 'Bold', // Perbaikan: Tambahkan mapping untuk 'Bold' string
    '300': 'Regular',
    '400': 'Regular',
    '500': 'Regular',
    '600': 'Bold',
    '700': 'Bold',
    '800': 'Bold',
    '900': 'Bold',
  };

  const mappedWeight = weightMap[weight] || 'Regular';

  // Pilih font berdasarkan script
  let fontPrefix = '';

  if (script === 'arabic') {
    fontPrefix = 'NotoSansArabic';
    // Arabic fonts tidak memiliki italic, selalu gunakan regular/bold
  } else if (script === 'japanese' || script === 'cjk') {
    fontPrefix = 'NotoSansJP';
    // Japanese fonts tidak memiliki italic, selalu gunakan regular/bold
  } else if (script === 'cyrillic') {
    fontPrefix = 'NotoSans';
    // Cyrillic bisa menggunakan italic jika tersedia
  } else {
    fontPrefix = 'NotoSans';
  }

  // Untuk Arabic dan Japanese, tidak ada italic - paksa ke regular/bold
  if (script === 'arabic' || script === 'japanese' || script === 'cjk') {
    const fallbackKey = `${fontPrefix}-${mappedWeight}`;
    if (FONT_MAP[fallbackKey]) {
      // Perbaikan: Log untuk debugging NotoSansJP
      if (fallbackKey.includes('NotoSansJP')) {
        console.log(`[FontMapper] Using NotoSansJP fallback: ${fallbackKey} (original: ${originalFont}, weight: ${weight})`);
      }
      return fallbackKey;
    }
    // Fallback ke Regular jika Bold tidak tersedia
    const regularFallback = `${fontPrefix}-Regular`;
    if (fallbackKey.includes('NotoSansJP')) {
      console.warn(`[FontMapper] NotoSansJP Bold not found, falling back to Regular: ${regularFallback}`);
    }
    return regularFallback;
  }

  // Untuk script lain, coba dengan italic jika diminta
  if (italic) {
    const italicKey = mappedWeight === 'Bold' ? `${fontPrefix}-BoldItalic` : `${fontPrefix}-Italic`;
    if (FONT_MAP[italicKey]) {
      return italicKey;
    }
  }

  // Fallback ke non-italic
  const fallbackKey = `${fontPrefix}-${mappedWeight}`;
  if (FONT_MAP[fallbackKey]) {
    return fallbackKey;
  }

  // Fallback terakhir
  return `${fontPrefix}-Regular`;
};

// build a key from the extracted block
export const fontKey = (family: string, weight: string, italic: boolean) => {
  const suffix = italic ? 'Italic' : '';

  // Mapping weight untuk MuseoSans sesuai permintaan:
  // normal -> MuseoSans-300
  // bold -> MuseoSans-700
  let w = '300'; // default normal
  if (weight === 'bold') {
    w = '700';
  } else if (weight === '600') {
    w = '500';
  } else if (weight === '900') {
    w = '900';
  } else if (weight === '100') {
    w = '100';
  } else if (weight === '500') {
    w = '500';
  } else if (weight === '700') {
    w = '700';
  } else if (weight === '300') {
    w = '300';
  }

  return `${family}-${w}${suffix}`;
};