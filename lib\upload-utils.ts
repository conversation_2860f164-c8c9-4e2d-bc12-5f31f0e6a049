import { upload } from '@vercel/blob/client';

export interface UploadResult {
  url: string;
  pathname: string;
  contentType: string;
  contentDisposition: string;
}

export async function uploadFile(
  file: File,
  type: 'cover' | 'pdf' | 'translated-pdf',
  additionalPath?: string
): Promise<UploadResult> {
  try {
    let pathname: string;
    
    switch (type) {
      case 'cover':
        pathname = `covers/${Date.now()}-${file.name}`;
        break;
      case 'pdf':
        pathname = `pdfs/original/${Date.now()}-${file.name}`;
        break;
      case 'translated-pdf':
        pathname = `pdfs/translated/${additionalPath || 'unknown'}-${Date.now()}-${file.name}`;
        break;
      default:
        pathname = `uploads/${Date.now()}-${file.name}`;
    }

    const result = await upload(pathname, file, {
      access: 'public',
      handleUploadUrl: '/api/upload',
    });

    return {
      url: result.url,
      pathname: result.pathname,
      contentType: result.contentType || file.type,
      contentDisposition: result.contentDisposition || `attachment; filename="${file.name}"`,
    };
  } catch (error) {
    console.error('Upload failed:', error);
    throw new Error(`Failed to upload ${type}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function uploadMultipleFiles(
  files: { file: File; type: 'cover' | 'pdf' | 'translated-pdf'; additionalPath?: string }[]
): Promise<UploadResult[]> {
  try {
    const uploadPromises = files.map(({ file, type, additionalPath }) =>
      uploadFile(file, type, additionalPath)
    );
    
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Multiple upload failed:', error);
    throw new Error(`Failed to upload files: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to validate file types
export function validateFile(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type);
}

// Helper function to validate file size (in MB)
export function validateFileSize(file: File, maxSizeMB: number): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
}

// Helper function for comprehensive file validation
export function validateUploadFile(
  file: File,
  type: 'cover' | 'pdf' | 'translated-pdf'
): { isValid: boolean; error?: string } {
  // Define allowed types and max sizes for each file type
  const validationRules = {
    cover: {
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'],
      maxSizeMB: 10, // 10MB for images
    },
    pdf: {
      allowedTypes: ['application/pdf'],
      maxSizeMB: 50, // 50MB for PDFs
    },
    'translated-pdf': {
      allowedTypes: ['application/pdf'],
      maxSizeMB: 50, // 50MB for translated PDFs
    },
  };

  const rules = validationRules[type];

  if (!validateFile(file, rules.allowedTypes)) {
    return {
      isValid: false,
      error: `Invalid file type. Allowed types: ${rules.allowedTypes.join(', ')}`,
    };
  }

  if (!validateFileSize(file, rules.maxSizeMB)) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${rules.maxSizeMB}MB`,
    };
  }

  return { isValid: true };
}
