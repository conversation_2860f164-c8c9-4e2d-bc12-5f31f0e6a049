import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { books, booksLanguages } from '@/drizzle/schema';
import { eq } from 'drizzle-orm';
import { put } from '@vercel/blob';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    const formData = await request.formData();

    const updateData: any = {};

    ['namaBuku', 'description', 'license', 'author', 'category'].forEach(field => {
      const value = formData.get(field);
      if (value !== null && value !== undefined) {
        updateData[field] = value;
      }
    });

    ['pages', 'harga', 'rating'].forEach(field => {
      const value = formData.get(field);
      if (value !== null && value !== undefined && value !== '') {
        updateData[field] = parseFloat(value as string);
      }
    });

    // Handle file uploads for coverImage and originalPdf
    const coverImage = formData.get('coverImage') as File;
    if (coverImage && coverImage.size > 0) {
      const { url } = await put(
        `covers/${Date.now()}-${coverImage.name}`,
        coverImage,
        { access: 'public' }
      );
      updateData.coverImageUrl = url;
    }

    const originalPdf = formData.get('originalPdf') as File;
    if (originalPdf && originalPdf.size > 0) {
      const { url } = await put(
        `pdfs/original/${Date.now()}-${originalPdf.name}`,
        originalPdf,
        { access: 'public' }
      );
      updateData.originalPdfUrl = url;
    }

    // Update book
    const [updatedBook] = await db
      .update(books)
      .set(updateData)
      .where(eq(books.id, id))
      .returning();

    // Handle languages update
    const languagesMetaString = formData.get('languages');
    if (languagesMetaString) {
      const langs = JSON.parse(languagesMetaString as string);

      // Delete existing languages for this book
      await db.delete(booksLanguages).where(eq(booksLanguages.idBuku, id));

      // Insert or update new/existing languages
      for (let i = 0; i < langs.length; i++) {
        const lang = langs[i];
        let translatedPdfUrl = lang.translatedPdfUrl; // Preserve existing URL if no new file

        // Check if a new file for this language was uploaded
        const languagePdfFile = formData.get(`languagePdf-${i}`) as File;

        if (languagePdfFile && languagePdfFile.size > 0) {
          const { url } = await put(
            `pdfs/translated/${lang.language}-${Date.now()}-${languagePdfFile.name}`,
            languagePdfFile,
            { access: 'public' }
          );
          translatedPdfUrl = url;
        }

        await db.insert(booksLanguages).values({
          idBuku: id,
          language: lang.language,
          translatedPdfUrl,
        });
      }
    }

    return NextResponse.json(updatedBook);
  } catch (error) {
    console.error("Error updating book:", error);
    return NextResponse.json({ error: 'Failed to update book' }, { status: 500 });
  }
}


export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    await db.delete(books).where(eq(books.id, id));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting book:", error); // Lebih baik log errornya
    return NextResponse.json({ error: 'Failed to delete book' }, { status: 500 });
  }
}


export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);

    const [book] = await db.select().from(books).where(eq(books.id, id));
    const languages = await db
      .select()
      .from(booksLanguages)
      .where(eq(booksLanguages.idBuku, id));

    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    return NextResponse.json({ ...book, languages });
  } catch (error) {
    console.error("Error fetching book:", error);
    return NextResponse.json({ error: 'Failed to fetch book' }, { status: 500 });
  }
}