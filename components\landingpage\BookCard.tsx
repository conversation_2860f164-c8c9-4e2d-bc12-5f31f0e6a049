import React, { useState } from 'react';
import Image from 'next/image';
import { BookAdjusted, BookLanguage } from '@/lib/database/books';
import { StarRating } from './StarRating';

interface BookCardProps {
  book: BookAdjusted;
  onStartReading?: (bookId: number, language?: string) => void;
  onDownload?: (bookId: number, language?: string) => void;
  onSubscription?: (bookId: number) => void;
  onDownloadPreview?: (bookId: number, language?: string) => void;
  isOwned?: boolean;
}

export default function BookCard({ 
  book, 
  onStartReading, 
  onDownload,
  onSubscription,
  onDownloadPreview,
  isOwned = false 
}: BookCardProps) {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('id'); // Default to Indonesian
  const [showSnackbar, setShowSnackbar] = useState(false);
  
  const getCurrentPdfUrl = () => {
    if (selectedLanguage === 'id') {
      return book.originalPdfUrl;
    }
    
    const languageVersion = book.languages.find(lang => lang.language === selectedLanguage);
    return languageVersion?.translatedPdfUrl || book.originalPdfUrl;
  };

  const getLanguageDisplay = (langCode: string) => {
    const languageNames: { [key: string]: string } = {
      'id': 'Bahasa Indonesia',
      'en': 'English',
      'zh': '中文',
      'ar': 'العربية',
      'es': 'Español',
      'fr': 'Français',
      'de': 'Deutsch',
      'ja': '日本語',
      'ko': '한국어',
      'ru': 'Русский'
    };
    
    return languageNames[langCode] || langCode.toUpperCase();
  };

  const getLanguageFlag = (langCode: string) => {
    const languageFlags: { [key: string]: string } = {
      'id': '🇮🇩',
      'en': '🇺🇸',
      'zh': '🇨🇳',
      'ar': '🇸🇦',
      'es': '🇪🇸',
      'fr': '🇫🇷',
      'de': '🇩🇪',
      'ja': '🇯🇵',
      'ko': '🇰🇷',
      'ru': '🇷🇺'
    };
    
    return languageFlags[langCode] || '🌐';
  };

  const handleSubscription = () => {
    if (onSubscription) {
      onSubscription(book.id);
      setShowSnackbar(true);
      // Hide snackbar after 3 seconds
      setTimeout(() => setShowSnackbar(false), 3000);
    }
  };

  // Get all available languages including Indonesian
  const allLanguages = [
    { language: 'id', translatedPdfUrl: book.originalPdfUrl },
    ...book.languages
  ];

  return (
    <div className="bg-white rounded-2xl overflow-hidden relative">
      {/* Snackbar */}
      {showSnackbar && (
        <div className="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <span className="text-xl">✓</span>
          <span className="font-quicksand font-medium">
            Book added to My Books successfully!
          </span>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-6 p-6">
        {/* Book Cover */}
        <div className="flex-shrink-0 flex justify-center md:justify-start">
          <div className="w-96 h-96 relative">
            <div className="absolute inset-0"></div>
            <div className="absolute inset-0 flex items-center justify-center p-3">
              {book.coverImageUrl ? (
                <Image 
                  src={book.coverImageUrl} 
                  alt={`Cover of ${book.namaBuku}`}
                  width={384}
                  height={384}
                  className="max-w-full max-h-full object-contain"
                  priority
                />
              ) : (
                <div className="text-6xl">📖</div>
              )}
            </div>
            <Image 
              src="/thumbnails/thumbnail-border.svg" 
              alt=""
              width={384}
              height={384}
              className="absolute inset-0 w-full h-full object-fill pointer-events-none z-20"
            />
            {/* Owned Badge */}
            {isOwned && (
              <div className="absolute top-2 right-2 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold z-30">
                Owned
              </div>
            )}
          </div>
        </div>

        {/* Details */}
        <div className="flex-1 text-center md:text-left">
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-quicksand">
            {book.namaBuku}
          </h3>

          <div className="mb-4 flex justify-center md:justify-start">
            <StarRating rating={book.rating} size={36} />
          </div>

          <div className="mb-4 space-y-2 text-gray-600 font-quicksand font-medium">
            <p><strong>Author:</strong> {book.author}</p>
            <p><strong>Category:</strong> {book.category}</p>
            <p><strong>Pages:</strong> {book.pages} pages</p>
            <p><strong>License:</strong> {book.license}</p>
          </div>

          {/* Language Selector - Horizontal Pills */}
          {allLanguages.length > 1 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-3 font-quicksand">
                Available Languages:
              </label>
              <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                {allLanguages.map((lang) => (
                  <button
                    key={lang.language}
                    onClick={() => setSelectedLanguage(lang.language)}
                    className={`px-4 py-2 rounded-full border-2 transition-all duration-200 font-quicksand font-medium flex items-center gap-2 ${
                      selectedLanguage === lang.language
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    <span className="text-lg">{getLanguageFlag(lang.language)}</span>
                    <span className="text-sm">{getLanguageDisplay(lang.language)}</span>
                  </button>
                ))}
              </div>
            </div>
          )}

          <p className="text-lg text-gray-700 mb-6 leading-relaxed font-quicksand font-medium">
            {book.description}
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start items-center">
            {isOwned ? (
              <button
                onClick={() => onStartReading && onStartReading(book.id, selectedLanguage)}
                className="relative w-[150px] h-[56px]"
                aria-label={`Read ${book.namaBuku}`}
              >
                <Image
                  src="/buttons/preview-button.svg"
                  alt=""
                  width={150}
                  height={56}
                  className="absolute inset-0"
                />
                <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                  Read Now
                </span>
              </button>
            ) : (
              <>
                <button
                  onClick={() => onStartReading && onStartReading(book.id, selectedLanguage)}
                  className="relative w-[150px] h-[56px]"
                  aria-label={`Start reading ${book.namaBuku}`}
                >
                  <Image
                    src="/buttons/preview-button.svg"
                    alt=""
                    width={150}
                    height={56}
                    className="absolute inset-0"
                  />
                  <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                    Preview
                  </span>
                </button>

                <button 
                  onClick={handleSubscription}
                  className="relative w-[150px] h-[56px]"
                  aria-label={`Subscription ${book.namaBuku}`}
                >
                  <Image
                    src="/buttons/purchase-button.svg"
                    alt=""
                    width={150}
                    height={56}
                    className="absolute inset-0"
                  />
                  <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                    Pick This Book
                  </span>
                </button>
              </>
            )}
          </div>

          {/* Download Preview Button */}
          {!isOwned && (
            <div className="mt-6 flex justify-center md:justify-start">
              <button 
                onClick={() => onDownloadPreview && onDownloadPreview(book.id, selectedLanguage)}
                className="relative w-[220px] h-[56px]"
                aria-label={`Download ${book.namaBuku} as PDF`}
              >
                <Image
                  src="/buttons/download-button.svg"
                  alt=""
                  width={220}
                  height={56}
                  className="absolute inset-0"
                />
                <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                  Download PDF Preview
                </span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}