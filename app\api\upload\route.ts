import { handleUpload, type <PERSON>leU<PERSON>loadBody } from '@vercel/blob/client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest): Promise<NextResponse> {
  const body = (await request.json()) as HandleUploadBody;

  try {
    const jsonResponse = await handleUpload({
      body,
      request,
      onBeforeGenerateToken: async (pathname, clientPayload) => {
        // Generate a client token for the browser to upload the file
        // ⚠️ Authenticate and authorize users before generating the token.
        // For now, we'll allow uploads but you should add authentication here

        // Determine allowed content types based on the pathname
        let allowedContentTypes: string[] = [];
        
        if (pathname.includes('cover') || pathname.includes('image')) {
          allowedContentTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'];
        } else if (pathname.includes('pdf')) {
          allowedContentTypes = ['application/pdf'];
        } else {
          // Default to both images and PDFs
          allowedContentTypes = [
            'image/jpeg', 
            'image/png', 
            'image/webp', 
            'image/jpg',
            'application/pdf'
          ];
        }

        return {
          allowedContentTypes,
          addRandomSuffix: true,
          tokenPayload: JSON.stringify({
            // optional, sent to your server on upload completion
            uploadedAt: new Date().toISOString(),
            clientPayload,
          }),
        };
      },
      onUploadCompleted: async ({ blob, tokenPayload }) => {
        // Get notified of client upload completion
        // ⚠️ This will not work on `localhost` websites,
        // Use ngrok or similar to get the full upload flow

        console.log('blob upload completed', blob, tokenPayload);

        try {
          // Run any logic after the file upload completed
          // For example, you could save the blob URL to your database
          console.log(`File uploaded successfully: ${blob.url}`);
        } catch (error) {
          console.error('Error processing upload completion:', error);
          throw new Error('Could not process upload completion');
        }
      },
    });

    return NextResponse.json(jsonResponse);
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 }, // The webhook will retry 5 times waiting for a 200
    );
  }
}
