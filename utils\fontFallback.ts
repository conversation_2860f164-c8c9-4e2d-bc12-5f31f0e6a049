// utils/fontFallback.ts
import { FONT_MAP, detectTextScript, getFallbackFont } from './fontMapper';

export interface FontFallbackResult {
  fontKey: string;
  needsFallback: boolean;
  originalFont: string;
  fallbackFont?: string;
  unsupportedChars?: string[];
}

// Cache untuk hasil pengecekan font support
const fontSupportCache = new Map<string, boolean>();

// Fungsi untuk mengecek apakah karakter didukung oleh font
export const checkCharacterSupport = async (fontPath: string, char: string): Promise<boolean> => {
  const cacheKey = `${fontPath}-${char.charCodeAt(0)}`;
  
  if (fontSupportCache.has(cacheKey)) {
    return fontSupportCache.get(cacheKey)!;
  }

  try {
    // Implementasi sederhana berdasarkan Unicode ranges
    const codePoint = char.charCodeAt(0);
    let isSupported = false;

    if (fontPath.includes('MuseoSans') || fontPath.includes('BookAntiqua')) {
      // Font Latin hanya mendukung Basic Latin, Latin-1 Supplement, dan Latin Extended
      isSupported = (
        (codePoint >= 0x0020 && codePoint <= 0x007F) || // Basic Latin
        (codePoint >= 0x00A0 && codePoint <= 0x00FF) || // Latin-1 Supplement
        (codePoint >= 0x0100 && codePoint <= 0x017F) || // Latin Extended-A
        (codePoint >= 0x0180 && codePoint <= 0x024F)    // Latin Extended-B
      );
    } else if (fontPath.includes('NotoSansArabic')) {
      // NotoSansArabic mendukung Arabic dan Latin
      isSupported = (
        (codePoint >= 0x0020 && codePoint <= 0x007F) || // Basic Latin
        (codePoint >= 0x00A0 && codePoint <= 0x00FF) || // Latin-1 Supplement
        (codePoint >= 0x0600 && codePoint <= 0x06FF) || // Arabic
        (codePoint >= 0x0750 && codePoint <= 0x077F) || // Arabic Supplement
        (codePoint >= 0x08A0 && codePoint <= 0x08FF) || // Arabic Extended-A
        (codePoint >= 0xFB50 && codePoint <= 0xFDFF) || // Arabic Presentation Forms-A
        (codePoint >= 0xFE70 && codePoint <= 0xFEFF)    // Arabic Presentation Forms-B
      );
    } else if (fontPath.includes('NotoSansJP')) {
      // NotoSansJP mendukung Japanese, Chinese, Korean, dan Latin
      isSupported = (
        (codePoint >= 0x0020 && codePoint <= 0x007F) || // Basic Latin
        (codePoint >= 0x00A0 && codePoint <= 0x00FF) || // Latin-1 Supplement
        (codePoint >= 0x3040 && codePoint <= 0x309F) || // Hiragana
        (codePoint >= 0x30A0 && codePoint <= 0x30FF) || // Katakana
        (codePoint >= 0x4E00 && codePoint <= 0x9FFF) || // CJK Unified Ideographs
        (codePoint >= 0x3400 && codePoint <= 0x4DBF) || // CJK Extension A
        (codePoint >= 0xAC00 && codePoint <= 0xD7AF)    // Hangul Syllables (Korean)
      );
    } else if (fontPath.includes('NotoSans-') && !fontPath.includes('Arabic') && !fontPath.includes('JP')) {
      // NotoSans umum mendukung Latin dan Cyrillic
      isSupported = (
        (codePoint >= 0x0020 && codePoint <= 0x007F) || // Basic Latin
        (codePoint >= 0x00A0 && codePoint <= 0x00FF) || // Latin-1 Supplement
        (codePoint >= 0x0100 && codePoint <= 0x017F) || // Latin Extended-A
        (codePoint >= 0x0180 && codePoint <= 0x024F) || // Latin Extended-B
        (codePoint >= 0x0400 && codePoint <= 0x04FF) || // Cyrillic
        (codePoint >= 0x0500 && codePoint <= 0x052F)    // Cyrillic Supplement
      );
    } else {
      // Font sistem (Helvetica, Arial, dll) - hanya Latin
      isSupported = (
        (codePoint >= 0x0020 && codePoint <= 0x007F) || // Basic Latin
        (codePoint >= 0x00A0 && codePoint <= 0x00FF)    // Latin-1 Supplement
      );
    }

    fontSupportCache.set(cacheKey, isSupported);
    return isSupported;
  } catch (error) {
    console.warn(`Error checking character support for ${char}:`, error);
    fontSupportCache.set(cacheKey, false);
    return false;
  }
};

// Fungsi untuk menganalisis teks dan menentukan font yang tepat
export const analyzeFontRequirements = async (
  text: string, 
  originalFontKey: string, 
  fontWeight: string, 
  italic: boolean
): Promise<FontFallbackResult> => {
  const originalFontPath = FONT_MAP[originalFontKey];
  
  if (!originalFontPath) {
    // Jika font asli tidak ditemukan, langsung gunakan fallback
    const fallbackFont = getFallbackFont(originalFontKey, text, fontWeight, italic);
    return {
      fontKey: fallbackFont,
      needsFallback: true,
      originalFont: originalFontKey,
      fallbackFont: fallbackFont
    };
  }

  // Cek setiap karakter dalam teks
  const unsupportedChars: string[] = [];
  const uniqueChars = [...new Set(text.split(''))];

  for (const char of uniqueChars) {
    if (char.trim() === '') continue; // Skip whitespace
    
    const isSupported = await checkCharacterSupport(originalFontPath, char);
    if (!isSupported) {
      unsupportedChars.push(char);
    }
  }

  // Jika ada karakter yang tidak didukung, gunakan fallback
  if (unsupportedChars.length > 0) {
    const fallbackFont = getFallbackFont(originalFontKey, text, fontWeight, italic);
    return {
      fontKey: fallbackFont,
      needsFallback: true,
      originalFont: originalFontKey,
      fallbackFont: fallbackFont,
      unsupportedChars
    };
  }

  // Semua karakter didukung, gunakan font asli
  return {
    fontKey: originalFontKey,
    needsFallback: false,
    originalFont: originalFontKey
  };
};

// Fungsi untuk mendapatkan font yang optimal untuk rendering
export const getOptimalFont = async (
  text: string,
  fontFamily: string,
  fontWeight: string,
  italic: boolean
): Promise<string> => {
  // Buat font key dari parameter
  const baseFontName = fontFamily.split('-')[0];
  let fontKey = '';

  if (baseFontName === 'MuseoSans') {
    const suffix = italic ? 'Italic' : '';
    const weightMap: Record<string, string> = {
      'normal': '300',
      'bold': '700',
      '300': '300',
      '500': '500',
      '700': '700',
      '900': '900',
    };
    const mappedWeight = weightMap[fontWeight] || '300';
    fontKey = `MuseoSans-${mappedWeight}${suffix}`;
  } else if (baseFontName === 'BookAntiqua') {
    fontKey = fontWeight === 'bold' ? 'BookAntiqua-Bold' : 'BookAntiqua';
  } else if (baseFontName === 'NotoSansArabic') {
    // Arabic fonts tidak memiliki italic
    const weight = fontWeight === 'bold' || fontWeight === '700' ? 'Bold' : 'Regular';
    fontKey = `NotoSansArabic-${weight}`;
  } else if (baseFontName === 'NotoSansJP') {
    // Japanese fonts tidak memiliki italic
    // Perbaikan: Pastikan weight detection yang lebih akurat
    const weight = (fontWeight === 'bold' || fontWeight === '700' || fontWeight === 'Bold') ? 'Bold' : 'Regular';
    fontKey = `NotoSansJP-${weight}`;
    console.log(`[FontFallback] NotoSansJP font key generated: ${fontKey} (from weight: ${fontWeight})`);
  } else {
    // Untuk font lain, gunakan fallback langsung jika diperlukan
    const script = detectTextScript(text);
    if (script !== 'latin') {
      return getFallbackFont(fontFamily, text, fontWeight, italic);
    }
    return fontFamily; // Gunakan font sistem
  }

  // Analisis kebutuhan font
  const analysis = await analyzeFontRequirements(text, fontKey, fontWeight, italic);
  return analysis.fontKey;
};

// Fungsi untuk logging font fallback (untuk debugging)
export const logFontFallback = (result: FontFallbackResult, text: string) => {
  if (result.needsFallback) {
    console.log(`Font fallback applied for text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
    console.log(`Original font: ${result.originalFont}`);
    console.log(`Fallback font: ${result.fallbackFont}`);
    if (result.unsupportedChars && result.unsupportedChars.length > 0) {
      console.log(`Unsupported characters: ${result.unsupportedChars.join(', ')}`);
    }
  }
};
