'use client';

import { useState, useEffect } from 'react';
import { Book, BookLanguage } from '@/drizzle/schema';
import { Quicksand } from 'next/font/google';

// Shadcn UI Imports
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CountryDropdown, Country } from '@/components/ui/country-dropdown';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

const quicksand = Quicksand({
  subsets: ['latin'],
  variable: '--font-quicksand',
  weight: ['300', '400', '500', '600', '700']
});

interface BookWithLanguages extends Omit<Book, 'harga'> {
  languages: BookLanguage[];
}

// Category options
const categories = [
  'Life',
  'Non-Fiction',
  'Art & Music',
  'Animals',
  'Fairy Tales',
  'Science',
  'Adventure',
  'Nature'
];

// Language mapping based on common country-language associations
const getLanguageFromCountry = (countryCode: string): string => {
  const countryLanguageMap: { [key: string]: string } = {
    'USA': 'en', 'GBR': 'en', 'CAN': 'en', 'AUS': 'en', 'NZL': 'en',
    'ESP': 'es', 'MEX': 'es', 'ARG': 'es', 'COL': 'es', 'PER': 'es',
    'FRA': 'fr', 'BEL': 'fr', 'CHE': 'fr', 'LUX': 'fr',
    'DEU': 'de', 'AUT': 'de',
    'JPN': 'ja',
    'SAU': 'ar', 'ARE': 'ar', 'EGY': 'ar', 'JOR': 'ar',
    'RUS': 'ru', 'BLR': 'ru', 'KAZ': 'ru',
    'ITA': 'it', 'VAT': 'it', 'SMR': 'it',
    'PRT': 'pt', 'BRA': 'pt', 'MOZ': 'pt',
    'NLD': 'nl', 'SUR': 'nl',
    'CHN': 'zh', 'TWN': 'zh', 'HKG': 'zh', 'SGP': 'zh',
    'KOR': 'ko', 'PRK': 'ko',
    'IND': 'hi', 'NPL': 'hi',
    'THA': 'th',
    'VNM': 'vi',
    'IDN': 'id', 'MYS': 'id',
    'TUR': 'tr',
    'GRC': 'el',
    'POL': 'pl',
    'SWE': 'sv', 'FIN': 'sv',
    'NOR': 'no',
    'DNK': 'da',
    'CZE': 'cs',
    'HUN': 'hu',
    'ROU': 'ro',
    'BGR': 'bg',
    'HRV': 'hr',
    'SRB': 'sr',
    'SVK': 'sk',
    'SVN': 'sl',
    'EST': 'et',
    'LVA': 'lv',
    'LTU': 'lt',
    'UKR': 'uk',
    'ISR': 'he',
    'IRN': 'fa',
    'AFG': 'fa',
  };
  
  return countryLanguageMap[countryCode] || 'en';
};

// Get country name for display
const getCountryName = (countryCode: string): string => {
  const countryNameMap: { [key: string]: string } = {
    'USA': 'United States',
    'GBR': 'United Kingdom',
    'ESP': 'Spain',
    'FRA': 'France',
    'DEU': 'Germany',
    'JPN': 'Japan',
    'SAU': 'Saudi Arabia',
    'RUS': 'Russia',
    'ITA': 'Italy',
    'PRT': 'Portugal',
    'BRA': 'Brazil',
    'CHN': 'China',
    'KOR': 'South Korea',
    'IND': 'India',
    'IDN': 'Indonesia',
  };
  
  return countryNameMap[countryCode] || countryCode;
};

// Loading Skeleton Component
const BookSkeleton = () => (
  <Card>
    <CardHeader>
      <Skeleton className="w-full h-48 rounded-lg mb-4" />
      <Skeleton className="h-6 w-3/4 mb-2" />
      <Skeleton className="h-4 w-full mb-1" />
      <Skeleton className="h-4 w-2/3" />
    </CardHeader>
    <CardContent>
      <Skeleton className="h-6 w-24 mb-2" />
      <div className="space-y-2 mb-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-2/3" />
        <Skeleton className="h-4 w-1/2" />
      </div>
      <div className="pt-4 border-t">
        <Skeleton className="h-5 w-32 mb-2" />
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-2/3" />
      </div>
    </CardContent>
    <CardFooter className="flex justify-end gap-3">
      <Skeleton className="h-9 w-16" />
      <Skeleton className="h-9 w-16" />
    </CardFooter>
  </Card>
);

export default function BookProductPage() {
  const [books, setBooks] = useState<BookWithLanguages[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editingBook, setEditingBook] = useState<BookWithLanguages | null>(null);
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);

  // Form states
  const [formData, setFormData] = useState({
    namaBuku: '',
    description: '',
    pages: '',
    license: '',
    author: '',
    rating: '',
    category: '',
    coverImage: null as File | null,
    originalPdf: null as File | null,
  });

  const [languages, setLanguages] = useState<Array<{
    country: Country | null;
    language: string;
    pdf: File | null;
    translatedPdfUrl?: string;
  }>>([{ country: null, language: '', pdf: null }]);

  useEffect(() => {
    fetchBooks();
  }, []);

  const fetchBooks = async () => {
    setPageLoading(true);
    try {
      const response = await fetch('/api/books');
      const data = await response.json();
      setBooks(data);
    } catch (error) {
      console.error('Error fetching books:', error);
    } finally {
      setPageLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    const data = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== null) data.append(key, value);
    });
    data.append('harga', '0');
    
    // Keep the JSON for the metadata
    const languagesMeta = languages.map(lang => ({
      language: lang.language,
      translatedPdfUrl: lang.translatedPdfUrl
    }));
    data.append('languages', JSON.stringify(languagesMeta));

    // Attach the PDF files individually
    languages.forEach((lang, index) => {
      if (lang.pdf) {
        data.append(`languagePdf-${index}`, lang.pdf);
      }
    });

    try {
      const endpoint = editingBook ? `/api/books/${editingBook.id}` : '/api/books';
      const method = editingBook ? 'PUT' : 'POST';

      const response = await fetch(endpoint, {
        method,
        body: data,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Server error:', errorData);
        alert(`Failed to save book: ${errorData.error || response.statusText}`);
        return;
      }

      setShowModal(false);
      resetForm();
      fetchBooks();
    } catch (error) {
      console.error('Error submitting book:', error);
      alert('An unexpected error occurred while submitting the book.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this book?')) return;

    try {
      const response = await fetch(`/api/books/${id}`, { method: 'DELETE' });
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Server error on delete:', errorData);
        alert(`Failed to delete book: ${errorData.error || response.statusText}`);
        return;
      }
      fetchBooks();
    } catch (error) {
      console.error('Error deleting book:', error);
      alert('An unexpected error occurred while deleting the book.');
    }
  };

  const resetForm = () => {
    setFormData({
      namaBuku: '',
      description: '',
      pages: '',
      license: '',
      author: '',
      rating: '',
      category: '',
      coverImage: null,
      originalPdf: null,
    });
    setLanguages([{ country: null, language: '', pdf: null }]);
    setEditingBook(null);
  };

  const openEditModal = (book: BookWithLanguages) => {
    setEditingBook(book);
    setFormData({
      namaBuku: book.namaBuku,
      description: book.description || '',
      pages: book.pages?.toString() || '',
      license: book.license || '',
      author: book.author || '',
      rating: book.rating?.toString() || '',
      category: book.category || '',
      coverImage: null,
      originalPdf: null,
    });
    setLanguages(book.languages.map(lang => ({
      country: null, // Will be set based on language if needed
      language: lang.language,
      pdf: null,
      translatedPdfUrl: lang.translatedPdfUrl ?? undefined,
    })));
    setShowModal(true);
  };

  const addLanguage = () => {
    setLanguages([...languages, { country: null, language: '', pdf: null }]);
  };

  const removeLanguage = (index: number) => {
    setLanguages(languages.filter((_, i) => i !== index));
  };

  const updateLanguage = (index: number, field: string, value: any) => {
    const updated = [...languages];
    updated[index] = { ...updated[index], [field]: value };
    setLanguages(updated);
  };

  const handleCountryChange = (index: number, country: Country) => {
    const updated = [...languages];
    updated[index] = { 
      ...updated[index], 
      country: country,
      language: getLanguageFromCountry(country.alpha3)
    };
    setLanguages(updated);
  };

  return (
    <div className={`${quicksand.variable} font-quicksand min-h-screen p-8`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">📚 Library : Publishers Panel</h1>
          <Button
            onClick={() => {
              resetForm();
              setShowModal(true);
            }}
            disabled={pageLoading}
          >
            Add New Book
          </Button>
        </div>

        {/* Books Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pageLoading ? (
            // Show loading skeletons
            Array.from({ length: 6 }).map((_, index) => (
              <BookSkeleton key={index} />
            ))
          ) : books.length === 0 ? (
            <div className="col-span-full flex flex-col items-center justify-center py-12">
              <div className="text-6xl mb-4">📚</div>
              <p className="text-center text-lg text-muted-foreground">No books to display.</p>
            </div>
          ) : (
            books.map((book) => (
              <Card key={book.id} className="animate-fade-in">
                <CardHeader>
                  <img
                    src={book.coverImageUrl || '/placeholder-book.png'}
                    alt={book.namaBuku}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                  <CardTitle className="text-xl">{book.namaBuku}</CardTitle>
                  <CardDescription>
                    {book.description ? `${book.description.substring(0, 100)}${book.description.length > 100 ? '...' : ''}` : 'No description.'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm text-muted-foreground mb-4">
                    <p>Category: {book.category || 'No category'}</p>
                    <p>Pages: {book.pages}</p>
                    <p>Author: {book.author}</p>
                    <p>Rating: {book.rating}</p>
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <h4 className="font-semibold mb-2">Available Translations:</h4>
                    {book.languages && book.languages.length > 0 ? (
                      <ul className="list-disc list-inside text-sm text-muted-foreground">
                        {book.languages.map((lang, idx) => (
                          <li key={idx}>
                            {lang.language}
                            {lang.translatedPdfUrl && (
                              <a href={lang.translatedPdfUrl} target="_blank" rel="noopener noreferrer" className="ml-2 text-primary hover:underline">
                                (View PDF)
                              </a>
                            )}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-sm text-muted-foreground italic">No translations available.</p>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end gap-3">
                  <Button
                    onClick={() => openEditModal(book)}
                    variant="outline"
                  >
                    Edit
                  </Button>
                  <Button
                    onClick={() => handleDelete(book.id)}
                    variant="destructive"
                  >
                    Delete
                  </Button>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
        {showModal && (
          <div
            className="fixed inset-0 z-40 bg-black/50"
            onClick={() => setShowModal(false)} // opsional: klik backdrop = tutup
          />
        )}
        {/* Modal */}
        <Dialog open={showModal} onOpenChange={setShowModal} modal={false}>
          <DialogContent className="font-quicksand max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl">
                {editingBook ? 'Edit Book' : 'Add New Book'}
              </DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="namaBuku">Book Title</Label>
                <Input
                  id="namaBuku"
                  type="text"
                  value={formData.namaBuku}
                  onChange={(e) => setFormData({ ...formData, namaBuku: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pick a category..." />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="pages">Pages</Label>
                  <Input
                    id="pages"
                    type="number"
                    value={formData.pages}
                    onChange={(e) => setFormData({ ...formData, pages: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="license">License</Label>
                  <Input
                    id="license"
                    type="text"
                    value={formData.license}
                    onChange={(e) => setFormData({ ...formData, license: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="author">Author</Label>
                  <Input
                    id="author"
                    type="text"
                    value={formData.author}
                    onChange={(e) => setFormData({ ...formData, author: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="rating">Rating</Label>
                  <Input
                    id="rating"
                    type="number"
                    step="0.1"
                    min="0"
                    max="5"
                    value={formData.rating}
                    onChange={(e) => setFormData({ ...formData, rating: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="coverImage">Cover Image {editingBook && '(Upload new to change)'}</Label>
                <Input
                  id="coverImage"
                  type="file"
                  accept="image/*"
                  onChange={(e) => setFormData({ ...formData, coverImage: e.target.files?.[0] || null })}
                />
                {editingBook?.coverImageUrl && !formData.coverImage && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Current: <a href={editingBook.coverImageUrl} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Image</a>
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="originalPdf">Original PDF {editingBook && '(Upload new to change)'}</Label>
                <Input
                  id="originalPdf"
                  type="file"
                  accept="application/pdf"
                  onChange={(e) => setFormData({ ...formData, originalPdf: e.target.files?.[0] || null })}
                />
                {editingBook?.originalPdfUrl && !formData.originalPdf && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Current: <a href={editingBook.originalPdfUrl} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View PDF</a>
                  </p>
                )}
              </div>

              {editingBook && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="block text-sm font-medium">Language & Translated PDF (Change language / Upload new to change)</Label>
                    <p className="text-sm text-muted-foreground">
                      Select a country to determine the translation language. The PDF Translator feature is available at
                    </p>
                    <Link href="/admin/translator" className="text-primary underline hover:text-primary/80">
                        admin/translator
                    </Link>
                  </div>
                  {languages.map((lang, index) => (
                    <div key={index} className="flex flex-col md:flex-row gap-2 items-center">
                      <div className="flex-1">
                        <CountryDropdown
                          placeholder="Pick Language..."
                          onChange={(country) => handleCountryChange(index, country)}
                          defaultValue={lang.country?.alpha3}
                        />
                      </div>
                      <Input
                        type="file"
                        accept="application/pdf"
                        onChange={(e) => updateLanguage(index, 'pdf', e.target.files?.[0] || null)}
                        className="flex-1"
                      />
                      {lang.translatedPdfUrl && !lang.pdf && (
                        <p className="text-sm text-muted-foreground mt-1 md:mt-0 md:ml-2">
                          Current: <a href={lang.translatedPdfUrl} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View PDF</a>
                        </p>
                      )}
                      <Button
                        type="button"
                        onClick={() => removeLanguage(index)}
                        variant="destructive"
                        className="flex-shrink-0 w-full md:w-auto"
                      >
                        Delete
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    onClick={addLanguage}
                    variant="outline"
                    className="mt-2"
                  >
                    Add Language
                  </Button>
                </div>
              )}

              <DialogFooter className="flex justify-end gap-4 mt-6">
                <Button
                  type="button"
                  onClick={() => setShowModal(false)}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : editingBook ? 'Update Book' : 'Save New Book'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}